#!/bin/bash

# 静默构建脚本 - 最小化日志输出
set -e

VERSION=$1
RELEASE_NOTES=$2

if [ -z "$VERSION" ]; then
    echo "❌ 请提供版本号"
    echo "使用方法: ./scripts/silent-build.sh 0.1.0 \"发布说明\""
    exit 1
fi

if [ -z "$RELEASE_NOTES" ]; then
    RELEASE_NOTES="新版本发布"
fi

echo "🚀 开始静默构建 v$VERSION"

# 更新版本号（静默）
./scripts/update-version.sh "$VERSION" > /dev/null 2>&1

# 构建前端（静默）
echo "🔨 构建前端..."
npm run build > /dev/null 2>&1

# 构建 Tauri 应用（静默）
echo "🔨 构建 Tauri 应用..."
cd src-tauri

# 设置环境变量来减少 Rust 编译器输出
export CARGO_TERM_QUIET=true
export RUST_LOG=error

# 静默构建
~/.cargo/bin/cargo-tauri build > /dev/null 2>&1

cd ..

echo "✅ 构建完成！"

# 显示构建产物
echo ""
echo "📦 构建产物："
find src-tauri/target -name "*.app.tar.gz" -o -name "*setup.exe" -o -name "*.msi" 2>/dev/null | while read file; do
    echo "  - $(basename "$file") ($(du -h "$file" | cut -f1))"
done

echo ""
echo "🎯 下一步："
echo "  运行发布脚本: ./scripts/build-and-release.sh $VERSION \"$RELEASE_NOTES\""
