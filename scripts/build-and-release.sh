#!/bin/bash

# Goldfish App 本地构建和发布脚本
# 使用方法: ./scripts/build-and-release.sh 0.1.0 "发布说明"

set -e

VERSION=$1
RELEASE_NOTES=$2

if [ -z "$VERSION" ]; then
    echo "❌ 请提供版本号"
    echo "使用方法: ./scripts/build-and-release.sh 0.1.0 \"发布说明\""
    exit 1
fi

if [ -z "$RELEASE_NOTES" ]; then
    RELEASE_NOTES="新版本发布"
fi

echo "🚀 开始构建 Goldfish App v$VERSION"

# 检查必要工具
if ! command -v gh &> /dev/null; then
    echo "❌ 请先安装 GitHub CLI: brew install gh"
    exit 1
fi

if ! command -v cargo &> /dev/null; then
    echo "❌ 请先安装 Rust 和 Cargo"
    exit 1
fi

# 检查是否安装了 tauri-cli
TAURI_CLI="$HOME/.cargo/bin/cargo-tauri"
if [ ! -f "$TAURI_CLI" ]; then
    echo "❌ 请先安装 Tauri CLI: cargo install tauri-cli"
    exit 1
fi

# 检查是否已登录 GitHub CLI
if ! gh auth status &> /dev/null; then
    echo "❌ 请先登录 GitHub CLI: gh auth login"
    exit 1
fi

# 更新版本号
echo "📝 更新版本号到 $VERSION"
./scripts/update-version.sh "$VERSION"

# 构建前端
echo "🔨 构建前端..."
npm run build

# 构建 Tauri 应用
echo "🔨 构建 Tauri 应用..."
"$TAURI_CLI" build --quiet 2>/dev/null || "$TAURI_CLI" build

# 创建 macOS 更新文件
echo "📦 创建 macOS 更新文件..."
if [ -d "src-tauri/target/release/bundle/macos/goldfish-app.app" ]; then
    cd src-tauri/target/release/bundle/macos

    # 清理可能存在的元数据文件
    find goldfish-app.app -name "._*" -delete 2>/dev/null || true
    find goldfish-app.app -name ".DS_Store" -delete 2>/dev/null || true

    # 使用 COPYFILE_DISABLE 环境变量防止包含资源分叉文件
    COPYFILE_DISABLE=1 tar -czf goldfish-app.app.tar.gz goldfish-app.app

    cd ../../../../..
    echo "✅ macOS 更新文件已创建"
else
    echo "⚠️ 未找到 macOS 应用包"
fi

# 签名文件
echo "🔐 签名构建产物..."
PRIVATE_KEY_PATH="$HOME/.tauri/myapp.key"

if [ ! -f "$PRIVATE_KEY_PATH" ]; then
    echo "❌ 私钥文件不存在: $PRIVATE_KEY_PATH"
    exit 1
fi

# 查找构建产物并签名
BUILD_DIR="src-tauri/target"

echo "🔍 查找构建产物..."
echo "构建目录: $BUILD_DIR"

# 列出所有构建产物
find "$BUILD_DIR" -type f -name "*.app.tar.gz" -o -name "*.msi" -o -name "*setup.exe" -o -name "*.deb" -o -name "*.AppImage" 2>/dev/null || echo "未找到构建产物"

# macOS 应用签名
MACOS_APP=$(find "$BUILD_DIR" -name "*.app.tar.gz" 2>/dev/null | head -1)
if [ -n "$MACOS_APP" ]; then
    echo "🔐 签名 macOS 应用: $MACOS_APP"
    "$TAURI_CLI" signer sign -f "$PRIVATE_KEY_PATH" -p "111333888" "$MACOS_APP"
else
    echo "⚠️ 未找到 macOS 应用文件"
fi

# Windows 应用签名 (MSI 或 NSIS)
WINDOWS_APP=$(find "$BUILD_DIR" -name "*.msi" -o -name "*setup.exe" 2>/dev/null | head -1)
if [ -n "$WINDOWS_APP" ]; then
    echo "🔐 签名 Windows 应用: $WINDOWS_APP"
    "$TAURI_CLI" signer sign -f "$PRIVATE_KEY_PATH" -p "111333888" "$WINDOWS_APP"
else
    echo "⚠️ 未找到 Windows 应用文件"
fi

# Linux 应用签名（如果存在）
LINUX_APP=$(find "$BUILD_DIR" -name "*.deb" -o -name "*.AppImage" 2>/dev/null | head -1)
if [ -n "$LINUX_APP" ]; then
    echo "🔐 签名 Linux 应用: $LINUX_APP"
    "$TAURI_CLI" signer sign -f "$PRIVATE_KEY_PATH" -p "111333888" "$LINUX_APP"
else
    echo "⚠️ 未找到 Linux 应用文件"
fi

# 创建 GitHub Release
echo "📦 创建 GitHub Release..."
gh release create "v$VERSION" \
    --repo "546200350/goofish-monitor" \
    --title "Goldfish App v$VERSION" \
    --notes "$RELEASE_NOTES" \
    --draft

# 上传构建产物
echo "📤 上传构建产物..."

# 上传所有构建产物和签名文件
echo "📤 上传构建产物..."

# 查找并上传所有签名的文件
UPLOAD_FILES=""

# 查找 macOS 文件
for file in $(find "$BUILD_DIR" -name "*.app.tar.gz" 2>/dev/null); do
    if [ -f "$file" ] && [ -f "$file.sig" ]; then
        UPLOAD_FILES="$UPLOAD_FILES $file $file.sig"
        echo "✅ 找到 macOS 文件: $(basename $file)"
    fi
done

# 查找 Windows 更新文件 (MSI 或 NSIS setup.exe)
for file in $(find "$BUILD_DIR" -name "*.msi" -o -name "*setup.exe" 2>/dev/null); do
    if [ -f "$file" ] && [ -f "$file.sig" ]; then
        UPLOAD_FILES="$UPLOAD_FILES $file $file.sig"
        echo "✅ 找到 Windows 更新文件: $(basename $file)"
    fi
done

# 查找 Linux 文件
for file in $(find "$BUILD_DIR" -name "*.deb" -o -name "*.AppImage" 2>/dev/null); do
    if [ -f "$file" ] && [ -f "$file.sig" ]; then
        UPLOAD_FILES="$UPLOAD_FILES $file $file.sig"
        echo "✅ 找到 Linux 文件: $(basename $file)"
    fi
done

# 生成 Tauri 更新清单
echo "📝 生成更新清单..."
MACOS_SIG=""
WINDOWS_SIG=""

# 读取签名文件内容
MACOS_APP=$(find "$BUILD_DIR" -name "*.app.tar.gz" 2>/dev/null | head -1)
if [ -n "$MACOS_APP" ] && [ -f "$MACOS_APP.sig" ]; then
    MACOS_SIG=$(cat "$MACOS_APP.sig")
fi

WINDOWS_APP=$(find "$BUILD_DIR" -name "*setup.exe" 2>/dev/null | head -1)
if [ -n "$WINDOWS_APP" ] && [ -f "$WINDOWS_APP.sig" ]; then
    WINDOWS_SIG=$(cat "$WINDOWS_APP.sig")
fi

# 生成更新清单 JSON
cat > update-manifest.json << EOF
{
  "version": "$VERSION",
  "notes": "$RELEASE_NOTES",
  "pub_date": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "platforms": {
    "darwin-x86_64": {
      "signature": "$MACOS_SIG",
      "url": "https://github.com/546200350/goofish-monitor/releases/download/v$VERSION/goldfish-app.app.tar.gz"
    },
    "darwin-aarch64": {
      "signature": "$MACOS_SIG",
      "url": "https://github.com/546200350/goofish-monitor/releases/download/v$VERSION/goldfish-app.app.tar.gz"
    },
    "windows-x86_64": {
      "signature": "$WINDOWS_SIG",
      "url": "https://github.com/546200350/goofish-monitor/releases/download/v$VERSION/goldfish-app_${VERSION}_x64-setup.exe"
    }
  }
}
EOF

# 上传文件
if [ -n "$UPLOAD_FILES" ]; then
    echo "📤 上传文件到 GitHub Release..."
    gh release upload "v$VERSION" $UPLOAD_FILES update-manifest.json --repo "546200350/goofish-monitor"
    echo "✅ 文件上传完成"
else
    echo "❌ 没有找到可上传的文件（需要构建产物和对应的签名文件）"
    exit 1
fi

# 发布 Release（从草稿变为正式发布）
echo "🎉 发布 Release..."
gh release edit "v$VERSION" --draft=false --repo "546200350/goofish-monitor"

echo "✅ 发布完成！"
echo "🔗 查看发布: https://github.com/546200350/goofish-monitor/releases/tag/v$VERSION"
