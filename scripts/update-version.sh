#!/bin/bash

# 安全的版本号更新脚本
# 使用方法: ./scripts/update-version.sh 0.1.1

set -e

VERSION=$1

if [ -z "$VERSION" ]; then
    echo "❌ 请提供版本号"
    echo "使用方法: ./scripts/update-version.sh 0.1.1"
    exit 1
fi

echo "📝 更新版本号到 $VERSION"

# 备份原文件
cp package.json package.json.bak
cp src-tauri/Cargo.toml src-tauri/Cargo.toml.bak
cp src-tauri/tauri.conf.json src-tauri/tauri.conf.json.bak

# 使用 Node.js 更新 package.json（最安全的方式）
node -e "
const fs = require('fs');
const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
pkg.version = '$VERSION';
fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2) + '\n');
console.log('✅ 已更新 package.json');
"

# 使用 Node.js 更新 tauri.conf.json
node -e "
const fs = require('fs');
const config = JSON.parse(fs.readFileSync('src-tauri/tauri.conf.json', 'utf8'));
config.version = '$VERSION';
fs.writeFileSync('src-tauri/tauri.conf.json', JSON.stringify(config, null, 2) + '\n');
console.log('✅ 已更新 tauri.conf.json');
"

# 使用更精确的 sed 命令更新 Cargo.toml
# 只更新 [package] 部分的第一个 version 行
awk -v new_version="$VERSION" '
BEGIN { in_package = 0; version_updated = 0 }
/^\[package\]/ { in_package = 1; print; next }
/^\[/ && !/^\[package\]/ { in_package = 0 }
in_package && /^version = / && !version_updated { 
    print "version = \"" new_version "\""
    version_updated = 1
    next 
}
{ print }
' src-tauri/Cargo.toml > src-tauri/Cargo.toml.tmp && mv src-tauri/Cargo.toml.tmp src-tauri/Cargo.toml

echo "✅ 已更新 Cargo.toml"

# 验证更新结果
echo ""
echo "🔍 验证更新结果："
echo "package.json: $(node -e "console.log(JSON.parse(require('fs').readFileSync('package.json', 'utf8')).version)")"
echo "tauri.conf.json: $(node -e "console.log(JSON.parse(require('fs').readFileSync('src-tauri/tauri.conf.json', 'utf8')).version)")"
echo "Cargo.toml: $(grep -A 5 '^\[package\]' src-tauri/Cargo.toml | grep '^version =' | head -1 | sed 's/version = "\(.*\)"/\1/')"

# 清理备份文件
rm -f package.json.bak src-tauri/Cargo.toml.bak src-tauri/tauri.conf.json.bak

echo ""
echo "✅ 版本号更新完成！"
