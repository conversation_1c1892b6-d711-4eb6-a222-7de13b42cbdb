<template>
  <div class="update-notification-container">
    <!-- 更新可用通知 -->
    <n-modal
      v-model:show="showUpdateModal"
      preset="dialog"
      title="🎉 发现新版本"
      :closable="false"
      :mask-closable="false"
      :close-on-esc="false"
    >
      <div class="update-content">
        <div class="version-info">
          <p class="version-text">
            <strong>新版本：</strong>{{ updateState.version }}
          </p>
          <div v-if="updateState.notes" class="release-notes">
            <p><strong>更新内容：</strong></p>
            <div class="notes-content">{{ updateState.notes }}</div>
          </div>
        </div>

        <!-- 下载进度 -->
        <div v-if="isDownloading" class="download-progress">
          <p>正在下载更新...</p>
          <n-progress
            type="line"
            :percentage="Math.round(updateState.progress)"
            :show-indicator="true"
            status="info"
          />
        </div>

        <!-- 安装状态 -->
        <div
          v-if="updateState.status === 'Installing'"
          class="installing-status"
        >
          <n-spin size="small" />
          <span class="ml-2">正在安装更新...</span>
        </div>

        <!-- 准备重启 -->
        <div v-if="isReadyToRestart" class="restart-ready">
          <n-alert type="success" title="更新安装完成">
            更新已安装完成，需要重启应用以应用更新。
          </n-alert>
        </div>

        <!-- 错误信息 -->
        <div v-if="updateState.status === 'Error'" class="error-info">
          <n-alert type="error" title="更新失败">
            {{ updateState.error }}
          </n-alert>
        </div>
      </div>

      <template #action>
        <div class="update-actions">
          <!-- 有更新可用时的按钮 -->
          <template v-if="hasUpdate && !isDownloading && !isReadyToRestart">
            <n-button @click="handleLater" quaternary> 稍后更新 </n-button>
            <n-button
              type="primary"
              @click="handleUpdate"
              :loading="isDownloading"
            >
              立即更新
            </n-button>
          </template>

          <!-- 准备重启时的按钮 -->
          <template v-if="isReadyToRestart">
            <n-button @click="handleLater" quaternary> 稍后重启 </n-button>
            <n-button type="primary" @click="handleRestart">
              立即重启
            </n-button>
          </template>

          <!-- 错误时的按钮 -->
          <template v-if="updateState.status === 'Error'">
            <n-button @click="handleClose" quaternary> 关闭 </n-button>
            <n-button type="primary" @click="handleRetry"> 重试 </n-button>
          </template>
        </div>
      </template>
    </n-modal>

    <!-- 检查更新按钮（可选，用于手动检查） -->
    <n-button
      v-if="showCheckButton"
      @click="handleCheckUpdate"
      :loading="isCheckingUpdate"
      size="small"
      quaternary
      class="check-update-btn"
    >
      <template #icon>
        <n-icon><RefreshOutline /></n-icon>
      </template>
      检查更新
    </n-button>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import {
  NModal,
  NButton,
  NProgress,
  NAlert,
  NSpin,
  NIcon,
  useMessage,
} from "naive-ui";
import { RefreshOutline } from "@vicons/ionicons5";
import {
  updateState,
  isCheckingUpdate,
  hasUpdate,
  isDownloading,
  isReadyToRestart,
  UpdateStatus,
  checkForUpdates,
  downloadAndInstall,
  restartApp,
} from "../services/updater";

// Props
const props = defineProps({
  // 是否显示手动检查更新按钮
  showCheckButton: {
    type: Boolean,
    default: false,
  },
  // 是否自动检查更新
  autoCheck: {
    type: Boolean,
    default: true,
  },
});

// 消息提示
const message = useMessage();

// 是否显示更新弹窗
const showUpdateModal = ref(false);

// 计算属性
const shouldShowModal = computed(() => {
  return (
    hasUpdate.value ||
    isDownloading.value ||
    isReadyToRestart.value ||
    updateState.status === UpdateStatus.ERROR
  );
});

// 监听是否应该显示弹窗
watch(shouldShowModal, (newVal) => {
  showUpdateModal.value = newVal;
});

// 监听更新状态变化
watch(
  () => updateState.status,
  (newStatus) => {
    console.log("🔄 更新状态变化:", newStatus);

    switch (newStatus) {
      case UpdateStatus.CHECKING:
        if (props.showCheckButton) {
          message.info("正在检查更新...");
        }
        break;
      case UpdateStatus.UP_TO_DATE:
        if (props.showCheckButton) {
          message.success("当前已是最新版本");
        }
        break;
      case UpdateStatus.AVAILABLE:
        message.info(`发现新版本 ${updateState.version}`);
        break;
      case UpdateStatus.DOWNLOADING:
        message.info("开始下载更新...");
        break;
      case UpdateStatus.INSTALLING:
        message.info("正在安装更新...");
        break;
      case UpdateStatus.READY_TO_RESTART:
        message.success("更新安装完成，可以重启应用");
        break;
      case UpdateStatus.ERROR:
        message.error(`更新失败: ${updateState.error}`);
        break;
    }
  }
);

// 事件处理
const handleCheckUpdate = async () => {
  try {
    await checkForUpdates();
  } catch (error) {
    console.error("检查更新失败:", error);
  }
};

const handleUpdate = async () => {
  try {
    await downloadAndInstall();
  } catch (error) {
    console.error("更新失败:", error);
  }
};

const handleRestart = async () => {
  try {
    await restartApp();
  } catch (error) {
    console.error("重启失败:", error);
    message.error("重启失败，请手动重启应用");
  }
};

const handleLater = () => {
  showUpdateModal.value = false;
  message.info("您可以稍后在设置中手动更新");
};

const handleClose = () => {
  showUpdateModal.value = false;
};

const handleRetry = async () => {
  try {
    await checkForUpdates();
  } catch (error) {
    console.error("重试检查更新失败:", error);
  }
};

// 生命周期
onMounted(async () => {
  if (props.autoCheck) {
    // 延迟3秒后自动检查更新，避免影响应用启动
    setTimeout(async () => {
      try {
        await checkForUpdates();
      } catch (error) {
        console.warn("自动检查更新失败:", error);
      }
    }, 3000);
  }
});
</script>

<style scoped>
.update-notification-container {
  position: relative;
}

.update-content {
  padding: 16px 0;
}

.version-info {
  margin-bottom: 16px;
}

.version-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.release-notes {
  margin-top: 12px;
}

.notes-content {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  margin-top: 8px;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

.download-progress {
  margin: 16px 0;
}

.installing-status {
  display: flex;
  align-items: center;
  margin: 16px 0;
}

.restart-ready {
  margin: 16px 0;
}

.error-info {
  margin: 16px 0;
}

.update-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.check-update-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.ml-2 {
  margin-left: 8px;
}
</style>
