<template>
  <div class="page">
    <n-card class="main-card" size="small">
      <!-- 控制栏：状态显示 + 操作按钮 -->
      <div class="control-bar">
        <!-- 左侧：状态显示 -->
        <div class="status-display">
          <div class="status-indicator">
            <div
              :class="[
                'status-dot',
                monitorStore.isRunning ? 'running' : 'stopped',
              ]"
            ></div>
          </div>
          <div class="status-info">
            <div class="status-message">{{ monitorStore.message }}</div>
            <div class="status-stats" v-if="monitorStore.stats">
              <span class="stat-item"
                >累计: {{ monitorStore.frontendStats.totalFoundItems }}</span
              >
              <span class="stat-item"
                >执行:
                {{ monitorStore.frontendStats.totalExecutionCount }}次</span
              >
              <span class="stat-item" v-if="monitorStore.currentUptime > 0">
                运行: {{ monitorStore.formattedUptime }}
              </span>
            </div>
          </div>
        </div>

        <!-- 右侧：控制按钮 -->
        <div class="control-buttons">
          <n-space :size="4">
            <!-- 监控开关按钮 -->
            <n-button
              :type="monitorStore.isRunning ? 'error' : 'primary'"
              size="small"
              ghost
              :loading="monitorStore.isLoading"
              @click="monitorStore.isRunning ? stopMonitor() : startMonitor()"
            >
              {{
                monitorStore.isLoading
                  ? monitorStore.isRunning
                    ? "停止中..."
                    : "启动中..."
                  : monitorStore.isRunning
                  ? "停止监控"
                  : "启动监控"
              }}
            </n-button>

            <n-button
              type="info"
              size="small"
              ghost
              :disabled="monitorStore.isLoading"
              @click="checkMonitorStatus"
            >
              刷新
            </n-button>
            <n-button
              type="warning"
              size="small"
              ghost
              :disabled="monitorStore.isLoading"
              @click="clearMonitorData"
            >
              清空
            </n-button>
          </n-space>
        </div>
      </div>

      <!-- 数据表格 -->
      <div ref="tableSectionRef" class="table-section">
        <n-data-table
          :columns="tableColumns"
          :data="sortedData"
          :max-height="dynamicTableHeight"
          :row-props="rowProps"
          striped
          virtual-scroll
        />
      </div>
    </n-card>

    <!-- 错误通知弹窗 -->
    <ErrorNotificationModal />

    <!-- 商品详情弹窗 -->
    <n-modal
      v-model:show="showItemDetail"
      preset="card"
      title="商品详情"
      style="width: 90%; max-width: 800px"
      :segmented="true"
      :closable="true"
      :mask-closable="true"
      @update:show="onModalVisibilityChange"
    >
      <div v-if="selectedItem" class="item-detail">
        <!-- 商品图片和标题 - 左右布局 -->
        <n-card size="small" class="detail-section">
          <div class="item-overview">
            <!-- 左侧：商品图片和二维码 -->
            <div class="image-section">
              <div class="image-container">
                <n-image
                  v-if="selectedItem.pic_url"
                  :src="selectedItem.pic_url"
                  alt="商品图片"
                  width="180"
                  height="180"
                  object-fit="cover"
                  fallback-src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='180' height='180'%3E%3Crect width='180' height='180' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23999'%3E图片加载失败%3C/text%3E%3C/svg%3E"
                  class="product-image"
                />
                <div v-else class="no-image">
                  <span>暂无图片</span>
                </div>
              </div>

              <!-- 二维码区域 -->
              <div v-if="qrCodeDataUrl" class="qr-code-section">
                <div class="qr-code-label">扫码查看</div>
                <img :src="qrCodeDataUrl" alt="商品二维码" class="qr-code" />
              </div>
              <div v-else-if="selectedItem.item_id" class="qr-code-section">
                <div class="qr-code-placeholder">
                  <span>生成二维码中...</span>
                </div>
              </div>
            </div>

            <!-- 右侧：商品标题和基本信息 -->
            <div class="title-section">
              <div class="item-title-container">
                <h3 class="item-title">{{ selectedItem.title }}</h3>
              </div>
              <div class="quick-info">
                <n-space vertical size="small">
                  <div class="info-row">
                    <n-tag type="success" size="large">
                      ¥{{ selectedItem.price }}
                    </n-tag>
                  </div>
                  <div class="info-row">
                    <n-text depth="2">卖家：</n-text>
                    <n-text>{{ selectedItem.user_nick_name }}</n-text>
                  </div>
                  <div class="info-row">
                    <n-text depth="2">地区：</n-text>
                    <n-text>{{ selectedItem.area }}</n-text>
                  </div>
                  <div class="info-row">
                    <n-text depth="2">商品ID：</n-text>
                    <n-text>{{ selectedItem.item_id }}</n-text>
                  </div>
                </n-space>
              </div>
            </div>
          </div>
        </n-card>

        <!-- 详细信息 -->
        <n-card size="small" class="detail-section">
          <n-descriptions label-placement="left" :column="2">
            <n-descriptions-item label="发布时间">
              {{ formatDetailTime(selectedItem.publish_time) }}
            </n-descriptions-item>
            <n-descriptions-item label="获取时间">
              {{ selectedItem.time }}
            </n-descriptions-item>
            <n-descriptions-item label="类别ID">
              {{ selectedItem.cat_id }}
            </n-descriptions-item>
            <n-descriptions-item label="卖家ID">
              <n-text depth="3" style="font-family: monospace; font-size: 12px">
                {{ selectedItem.seller_id }}
              </n-text>
            </n-descriptions-item>
          </n-descriptions>
        </n-card>
      </div>

      <!-- 弹窗底部操作按钮 -->
      <template #footer>
        <n-space justify="space-between">
          <n-button
            type="error"
            ghost
            @click="blockSeller"
            :disabled="!selectedItem.seller_id || !selectedItem.user_nick_name"
            :loading="blockingLoading"
          >
            拉黑商家
          </n-button>
          <n-space>
            <n-button @click="showItemDetail = false"> 关闭 </n-button>
            <n-button
              type="primary"
              @click="openItemLink"
              :disabled="!selectedItem.item_id"
            >
              查看详情
            </n-button>
          </n-space>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick, h } from "vue";
import QRCode from "qrcode";
import { openUrl } from "@tauri-apps/plugin-opener";
import { safeInvoke } from "../utils/tauri";
import { createDiscreteApi } from "naive-ui";
import { useMonitorStore } from "../stores/monitor";
import { processItemsImageUrls, processProductImageUrl } from "../utils/image";
import ErrorNotificationModal from "./ErrorNotificationModal.vue";

const { message } = createDiscreteApi(["message"]);
const monitorStore = useMonitorStore();

// 直接使用 store，不要赋值给本地变量以保持响应式
// const monitorStatus = monitorStore.isRunning; // ❌ 这样会失去响应式
// const monitorMessage = monitorStore.message;   // ❌ 这样会失去响应式
// const monitorStats = monitorStore.stats;       // ❌ 这样会失去响应式
// const monitorData = monitorStore.data;         // ❌ 这样会失去响应式
// const isLoading = monitorStore.isLoading;      // ❌ 这样会失去响应式

// 定义表格列配置
const tableColumns = ref([
  {
    title: "发布时间",
    key: "publish_time",
    width: 100,
    render: (row) => formatDetailTime(row.publish_time),
  },
  { title: "商品标题", key: "title", width: 200, ellipsis: { tooltip: true } },
  { title: "价格", key: "price", width: 80 },
  { title: "卖家", key: "user_nick_name", width: 120 },
  { title: "地区", key: "area", width: 100 },
  {
    title: "匹配关键词",
    key: "matched_keywords",
    width: 150,
    render: (row) => {
      // 直接使用后端提供的匹配关键词
      const matchedKeywords = row.matched_keywords || [];

      if (matchedKeywords.length === 0) {
        return h("span", { style: "color: #999;" }, "无");
      }
      return h(
        "div",
        { style: "display: flex; flex-wrap: wrap; gap: 4px;" },
        matchedKeywords.map((keyword) =>
          h(
            "n-tag",
            {
              type: "info",
              size: "small",
              style: "margin: 1px;",
            },
            keyword
          )
        )
      );
    },
  },
]);

// 计算属性：按发布时间倒序排列的数据（最新的在前面）
const sortedData = computed(() => {
  return [...monitorStore.data].sort((a, b) => {
    // 按发布时间倒序排列（最新的在前面）
    return new Date(b.publish_time) - new Date(a.publish_time);
  });
});

// 使用 store 的方法
const startMonitor = async () => {
  const result = await monitorStore.startMonitor();
  if (result.success) {
    message.success(result.message);
  } else {
    message.error(result.message);
  }
};

const stopMonitor = async () => {
  const result = await monitorStore.stopMonitor();
  if (result.success) {
    message.success(result.message);
  } else {
    message.error(result.message);
  }
};

const checkMonitorStatus = async () => {
  await monitorStore.checkStatus();
};

const clearMonitorData = () => {
  monitorStore.clearData();
  message.success("监控数据和累计统计已清空");
};

// 直接定义商品详情相关的状态和方法
const showItemDetail = ref(false);
const selectedItem = ref({});
const blockingLoading = ref(false);

const openItemDetail = (item) => {
  // 处理商品图片URL，将HTTP转换为HTTPS
  const processedItem = {
    ...item,
    pic_url: processProductImageUrl(item.pic_url),
  };
  selectedItem.value = processedItem;
  showItemDetail.value = true;
};

const closeItemDetail = () => {
  showItemDetail.value = false;
  selectedItem.value = {};
};

// 拉黑商家功能
const blockSeller = async () => {
  if (!selectedItem.value.seller_id || !selectedItem.value.user_nick_name) {
    message.error("卖家信息不完整，无法拉黑");
    return;
  }

  try {
    blockingLoading.value = true;

    // 检查 Tauri 环境
    console.log("Tauri 环境检测:", window.__TAURI__ !== undefined);
    console.log("当前协议:", window.location.protocol);

    // 获取当前配置
    const currentConfig = { ...monitorStore.config };

    // 确保 blocked_sellers 字段存在（兼容老版本）
    if (!currentConfig.blocked_sellers) {
      currentConfig.blocked_sellers = [];
    }

    // 检查是否已经存在
    const exists = currentConfig.blocked_sellers.some(
      (seller) => seller.seller_id === selectedItem.value.seller_id
    );

    if (exists) {
      message.warning("该卖家已在黑名单中");
      return;
    }

    currentConfig.blocked_sellers.push({
      seller_name: selectedItem.value.user_nick_name,
      seller_id: selectedItem.value.seller_id,
    });

    console.log(
      "添加拉黑卖家后的 currentConfig.blocked_sellers:",
      currentConfig.blocked_sellers
    );

    // 先获取完整的当前配置作为基础
    const fullCurrentConfig = await safeInvoke("config_get");

    // 转换为后端期望的格式（确保字段名正确，包含所有必要字段）
    const backendConfig = {
      interval_seconds: currentConfig.interval_seconds,
      target_page_count: currentConfig.target_page_count,
      keywords: currentConfig.keywords,
      exclude_keywords: currentConfig.exclude_keywords,
      min_price: currentConfig.min_price,
      max_price: currentConfig.max_price,
      notify_enabled: currentConfig.notify_enabled,
      login_cookie: currentConfig.login_cookie || null,
      keyword_price_rules: currentConfig.keywords_price_rules || [], // 注意字段名转换
      dingtalk_hooks: currentConfig.dingtalk_hooks || [],
      rsa_activation_info: fullCurrentConfig.rsa_activation_info || {
        activation_code: null,
      }, // 必需字段：RSA激活信息
      display_limit: currentConfig.display_limit,
      blocked_sellers: currentConfig.blocked_sellers || [], // 拉黑卖家列表
      selected_provinces: currentConfig.selected_provinces || [], // 选中的省份列表
    };

    // 使用现有的配置更新命令
    await safeInvoke("config_update", {
      config: backendConfig,
    });

    message.success(
      `已将卖家 "${selectedItem.value.user_nick_name}" 加入黑名单`
    );

    // 关闭弹窗
    showItemDetail.value = false;
    selectedItem.value = {};

    // 重新加载配置以更新黑名单
    await monitorStore.loadConfig();
  } catch (error) {
    console.error("拉黑卖家失败:", error);
    message.error("拉黑卖家失败: " + error);
  } finally {
    blockingLoading.value = false;
  }
};

// 二维码状态
const qrCodeDataUrl = ref("");

// 表格动态高度相关
const tableSectionRef = ref(null);
const dynamicTableHeight = ref(450); // 默认高度

// 表格行属性配置
const rowProps = (row) => {
  return {
    style: "cursor: pointer;",
    onClick: () => {
      openItemDetail(row);
    },
  };
};

// 格式化详细时间显示 - 正确处理时间戳格式
const formatDetailTime = (timestampStr) => {
  if (!timestampStr) return "未知";

  try {
    const timestamp = parseInt(timestampStr);
    if (isNaN(timestamp)) return timestampStr;

    // 自动判断是秒还是毫秒时间戳
    const timestampMs = timestamp > 9999999999 ? timestamp : timestamp * 1000;
    const date = new Date(timestampMs);

    if (isNaN(date.getTime())) return timestampStr;

    // 返回时分秒格式
    return date.toLocaleString("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    });
  } catch (error) {
    return timestampStr;
  }
};

// 打开商品链接
const openItemLink = async () => {
  if (selectedItem.value && selectedItem.value.item_id) {
    try {
      const goofishUrl = `https://www.goofish.com/item?id=${selectedItem.value.item_id}`;
      await openUrl(goofishUrl);
    } catch (error) {
      console.error("打开链接失败:", error);
    }
  }
};

// 生成二维码
const generateQRCode = async (itemId) => {
  if (!itemId) {
    qrCodeDataUrl.value = "";
    return;
  }

  try {
    const fleamarketUrl = `fleamarket://item?id=${itemId}`;
    const dataUrl = await QRCode.toDataURL(fleamarketUrl, {
      width: 180,
      margin: 1,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
    });
    qrCodeDataUrl.value = dataUrl;
  } catch (error) {
    console.error("生成二维码失败:", error);
    qrCodeDataUrl.value = "";
  }
};

// 计算表格动态高度 - 基于窗口高度
const calculateTableHeight = () => {
  nextTick(() => {
    try {
      // 获取窗口高度
      const windowHeight = window.innerHeight;

      // 计算其他固定元素的高度
      const headerHeight = 60; // 页面标题区域
      const tabsHeight = 48; // 标签页高度
      const controlsHeight = 60; // 控制栏高度（监控面板的控制栏稍高）
      const padding = 40; // 页面内边距和其他间距

      // 计算表格可用高度
      const availableHeight =
        windowHeight - headerHeight - tabsHeight - controlsHeight - padding;

      // 设置最小高度为200px，最大高度为可用高度
      const calculatedHeight = Math.max(
        200,
        Math.min(availableHeight, windowHeight * 0.75)
      );

      dynamicTableHeight.value = calculatedHeight;

      // 技术调试信息，生产环境中移除
      if (process.env.NODE_ENV === "development") {
        console.log(
          `🔧 动态计算监控表格高度: ${calculatedHeight}px (窗口高度: ${windowHeight}px, 可用高度: ${availableHeight}px)`
        );
      }
    } catch (error) {
      console.warn("计算表格高度失败:", error);
      // 出错时使用默认高度
      dynamicTableHeight.value = 450;
    }
  });
};

// 窗口尺寸变化处理
const handleResize = () => {
  calculateTableHeight();
};

// 组件挂载时设置
onMounted(() => {
  // 监听窗口尺寸变化
  window.addEventListener("resize", handleResize);

  // 延迟执行，确保DOM完全渲染
  nextTick(() => {
    // 初始计算
    calculateTableHeight();
  });
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});

// 监听选中商品变化，自动生成二维码
watch(
  () => selectedItem.value?.item_id,
  (newItemId) => {
    // 技术调试信息，生产环境中移除
    if (process.env.NODE_ENV === "development") {
      console.log("🔍 选中商品变化:", newItemId);
    }
    if (newItemId && showItemDetail.value) {
      generateQRCode(newItemId);
    } else {
      qrCodeDataUrl.value = "";
    }
  },
  { immediate: true }
);

// 使用新的浏览器服务打开商品链接
async function openItemInBrowser(url) {
  try {
    const windowLabel = await safeInvoke("browser_open", {
      targetUrl: url,
      matchUrlPattern: "goofish.com/item",
      windowLabel: `item_${Date.now()}`,
      callbackEnabled: false, // 不需要自动关闭
    });
    // 技术调试信息，生产环境中移除
    if (process.env.NODE_ENV === "development") {
      console.log("浏览器窗口已打开:", windowLabel);
    }
  } catch (error) {
    console.error("打开浏览器失败:", error);
  }
}
</script>

<style scoped>
.page {
  margin: 0 auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--n-base-color);
}

.page-header {
  margin-bottom: 0.75rem;
}

.page-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.25;
}

.main-card {
  border-radius: 0.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 修复卡片内容样式 */
.main-card :deep(.n-card__content) {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
}

/* 控制栏样式 */
.control-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  border-radius: 0.5rem;
}

.status-display {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
}

.status-dot.running {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
}

.status-dot.stopped {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

.status-info {
  font-size: 13px;
}

.status-message {
  font-weight: 500;
  margin-bottom: 4px;
}

.status-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.stat-item {
  white-space: nowrap;
}

.control-buttons {
  flex-shrink: 0;
}

.table-section {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex child能正确收缩 */
}

/* 滚动条美化 */
.main-card :deep(.n-card__content)::-webkit-scrollbar {
  width: 8px;
}

.main-card :deep(.n-card__content)::-webkit-scrollbar-track {
  background: var(--n-scrollbar-color);
  border-radius: 4px;
}

.main-card :deep(.n-card__content)::-webkit-scrollbar-thumb {
  background: var(--n-scrollbar-color-hover);
  border-radius: 4px;
}

.main-card :deep(.n-card__content)::-webkit-scrollbar-thumb:hover {
  background: var(--n-scrollbar-color-pressed);
}

/* 表格样式优化 */
.table-section :deep(.n-data-table) {
  /* 移除固定高度，让max-height属性控制 */
  display: flex;
  flex-direction: column;
}

/* 表格行hover效果 */
.table-section :deep(.n-data-table-tbody .n-data-table-tr:hover) {
  background-color: var(--n-table-color-hover);
  transition: background-color 0.3s ease;
}

/* 商品详情弹窗样式 */
.item-detail {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 65vh;
  overflow-y: auto;
  padding-bottom: 8px;
}

.detail-section {
  margin-bottom: 0;
}

.detail-section :deep(.n-card__content) {
  padding: 16px;
}

/* 商品概览左右布局 */
.item-overview {
  display: flex;
  gap: 20px;
  min-height: 180px;
}

.image-section {
  width: 180px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.image-container {
  display: flex;
  align-items: flex-start;
}

.product-image {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.product-image:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: scale(1.02);
}

.no-image {
  width: 180px;
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--n-color-embedded);
  border-radius: 8px;
  color: var(--n-text-color-disabled);
  font-size: 14px;
}

.title-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.item-title-container {
  flex: 1;
}

.item-title {
  margin: 0;
  line-height: 1.5;
  word-break: break-word;
  font-size: 16px;
  font-weight: 500;
  color: var(--n-text-color);
}

.quick-info {
  padding: 16px;
  background: var(--n-color-embedded);
  border-radius: 6px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.qr-code-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.qr-code-label {
  font-size: 12px;
  color: var(--n-text-color-disabled);
  font-weight: 500;
}

.qr-code {
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.qr-code-placeholder {
  width: 180px;
  height: 180px;
  background: var(--n-color-embedded);
  border: 1px dashed var(--n-border-color);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--n-text-color-disabled);
  font-size: 12px;
}

@media (max-width: 768px) {
  .page {
    max-width: 100%;
    height: 100%;
  }

  .page-title {
    font-size: 18px;
  }

  .main-card :deep(.n-card__content) {
    padding: 16px;
  }

  /* 移动端控制栏优化 */
  .control-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    min-height: auto;
  }

  .status-display {
    justify-content: center;
  }

  .control-buttons {
    align-self: stretch;
  }

  .control-buttons :deep(.n-space) {
    flex-wrap: wrap;
    justify-content: center;
  }

  .control-buttons :deep(.n-button) {
    flex: 1;
    min-width: 80px;
  }

  /* 移动端弹窗优化 */
  .item-detail {
    max-height: 60vh;
  }

  .detail-section :deep(.n-descriptions) {
    --n-label-width: 80px;
  }

  .detail-section :deep(.n-descriptions-item-label) {
    font-size: 12px;
  }

  .detail-section :deep(.n-descriptions-item-content) {
    font-size: 12px;
  }

  /* 移动端商品概览布局调整为上下排列 */
  .item-overview {
    flex-direction: column;
    gap: 16px;
    min-height: auto;
  }

  .image-section {
    width: 120px;
    align-self: center;
  }

  .product-image {
    width: 120px !important;
    height: 120px !important;
  }

  .no-image {
    width: 120px;
    height: 120px;
    font-size: 12px;
  }

  .qr-code {
    width: 120px;
    height: 120px;
  }

  .qr-code-placeholder {
    width: 120px;
    height: 120px;
    font-size: 10px;
  }

  .item-title {
    font-size: 14px;
  }

  .quick-info {
    padding: 12px;
  }

  .info-row {
    font-size: 13px;
  }
}
</style> 
