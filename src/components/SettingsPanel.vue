<template>
  <div class="page">
    <div class="main-container">
      <!-- 搜索关键词 & 价格范围卡片 -->
      <n-card class="config-card" size="small">
        <template #header>
          <div class="card-header">
            <h3 class="section-title">搜索关键词 & 价格范围</h3>
            <div style="display: flex; align-items: center; gap: 8px">
              <n-button type="primary" ghost @click="openKeywordsPriceModal">
                管理搜索规则
              </n-button>
            </div>
          </div>
        </template>

        <n-space direction="vertical" size="medium">
          <!-- 关键词规则预览 -->
          <div
            v-if="monitorStore.config.keywords_price_rules.length > 0"
            class="rules-preview"
          >
            <n-space size="small" :wrap="true">
              <n-tag
                v-for="(rule, index) in monitorStore.config
                  .keywords_price_rules"
                :key="index"
                type="default"
                size="medium"
                class="rule-tag"
              >
                {{ rule.keyword }} (¥{{ rule.min_price || 0 }} - ¥{{
                  rule.max_price || 1000
                }})
              </n-tag>
            </n-space>
          </div>
        </n-space>
      </n-card>

      <!-- 屏蔽词设置卡片 -->
      <n-card class="config-card" size="small">
        <template #header>
          <h3 class="section-title">屏蔽词设置</h3>
          <n-text depth="3" style="font-size: 12px">
            添加屏蔽词，含有这些词的商品将被过滤掉
          </n-text>
        </template>
        <n-space direction="vertical" size="medium">
          <n-dynamic-tags
            v-model:value="monitorStore.config.exclude_keywords"
            :max="20"
            placeholder="输入屏蔽词后按回车添加"
            size="medium"
            @update:value="monitorStore.debouncedSaveConfig"
          />
        </n-space>
      </n-card>

      <!-- 监控参数卡片 -->
      <n-card class="config-card" size="small">
        <template #header>
          <h3 class="section-title">监控参数</h3>
        </template>

        <n-grid x-gap="12" :cols="2">
          <n-grid-item>
            <div class="param-item">
              <n-text class="param-label">监控频率 (秒)</n-text>
              <n-input-number
                v-model:value="monitorStore.config.interval_seconds"
                :min="1"
                :max="300"
                placeholder="间隔秒数"
                size="medium"
                style="width: 100%"
                @blur="monitorStore.debouncedSaveConfig"
              />
            </div>
          </n-grid-item>
          <n-grid-item>
            <div class="param-item">
              <n-text class="param-label">显示条数</n-text>
              <n-input-number
                v-model:value="monitorStore.config.target_page_count"
                :min="1"
                :max="500"
                placeholder="显示条数"
                size="medium"
                style="width: 100%"
                @blur="monitorStore.debouncedSaveConfig"
              />
            </div>
          </n-grid-item>
        </n-grid>
      </n-card>

      <!-- 区域省份选择卡片 -->
      <n-card class="config-card" size="small">
        <template #header>
          <div class="card-header">
            <h3 class="section-title">区域省份筛选</h3>
            <n-text depth="3" style="font-size: 12px">
              选择要监控的省份区域，不选择则监控全国
            </n-text>
          </div>
        </template>

        <n-space direction="vertical" size="medium">
          <!-- 省份选择器 -->
          <div class="province-selector">
            <n-select
              v-model:value="selectedProvinceIds"
              :options="provinceOptions"
              placeholder="选择省份（可多选）"
              multiple
              filterable
              clearable
              size="medium"
              :max-tag-count="3"
              @update:value="onProvinceSelectionChange"
            />
          </div>

          <!-- 已选省份预览 -->
          <div
            v-if="monitorStore.config.selected_provinces.length > 0"
            class="selected-provinces-preview"
          >
            <n-text
              depth="3"
              style="font-size: 12px; margin-bottom: 8px; display: block"
            >
              已选择
              {{ monitorStore.config.selected_provinces.length }} 个省份：
            </n-text>
            <n-space size="small" :wrap="true">
              <n-tag
                v-for="province in monitorStore.config.selected_provinces"
                :key="province.id"
                type="info"
                size="small"
                closable
                @close="removeProvince(province.id)"
              >
                {{ province.name }}
              </n-tag>
            </n-space>
          </div>
        </n-space>
      </n-card>

      <!-- 通知设置卡片 -->
      <n-card class="config-card" size="small" style="display: none">
        <template #header>
          <h3 class="section-title">通知设置</h3>
        </template>

        <div class="notification-setting">
          <n-checkbox
            v-model:checked="monitorStore.config.notify_enabled"
            size="medium"
            @update:checked="monitorStore.debouncedSaveConfig"
          >
            启用系统通知
          </n-checkbox>
        </div>
      </n-card>

      <!-- 钉钉推送配置卡片 -->
      <n-card class="config-card" size="small">
        <template #header>
          <div class="card-header">
            <h3 class="section-title">钉钉推送配置</h3>
            <div style="display: flex; align-items: center; gap: 8px">
              <n-switch
                v-model:value="monitorStore.config.dingtalk_enabled"
                @update:value="saveDingtalkEnabled"
                size="small"
              >
                <template #checked>开启</template>
                <template #unchecked>关闭</template>
              </n-switch>
              <n-button
                type="primary"
                ghost
                @click="openDingtalkModal"
                :disabled="!monitorStore.config.dingtalk_enabled"
              >
                配置Hook
              </n-button>
              <n-button
                type="info"
                ghost
                @click="testAllDingtalkHooks"
                :loading="testingHooks"
                :disabled="
                  !monitorStore.config.dingtalk_enabled ||
                  monitorStore.config.dingtalk_hooks.length === 0
                "
              >
                {{ testingHooks ? "测试中..." : "测试推送" }}
              </n-button>
            </div>
          </div>
        </template>

        <n-space vertical size="medium">
          <div v-if="!monitorStore.config.dingtalk_enabled">
            <n-alert type="info" style="margin-bottom: 16px">
              钉钉推送功能已关闭，开启后可配置钉钉群机器人推送新商品通知
            </n-alert>
          </div>

          <div v-else>
            <n-text depth="3">
              配置钉钉群机器人Webhook，发现新商品时自动推送通知
            </n-text>

            <div v-if="monitorStore.config.dingtalk_hooks.length > 0">
              <n-text strong
                >已配置
                {{ monitorStore.config.dingtalk_hooks.length }} 个Hook:</n-text
              >
              <div style="margin-top: 8px">
                <n-tag
                  v-for="(hook, index) in monitorStore.config.dingtalk_hooks"
                  :key="index"
                  type="info"
                  style="margin: 4px 8px 4px 0"
                  closable
                  @close="removeDingtalkHook(index)"
                >
                  Hook {{ index + 1 }}
                </n-tag>
              </div>
            </div>

            <div v-else>
              <n-text depth="3">暂未配置钉钉推送</n-text>
            </div>
          </div>
        </n-space>
      </n-card>

      <!-- 拉黑卖家管理卡片 -->
      <n-card class="config-card" size="small">
        <template #header>
          <div class="card-header">
            <h3 class="section-title">拉黑卖家管理</h3>
            <n-text depth="3" style="font-size: 12px">
              管理已拉黑的卖家，被拉黑的卖家商品不会出现在监控结果中
            </n-text>
          </div>
        </template>

        <n-space vertical size="medium">
          <div
            v-if="
              monitorStore.config.blocked_sellers &&
              Array.isArray(monitorStore.config.blocked_sellers) &&
              monitorStore.config.blocked_sellers.length > 0
            "
          >
            <n-text strong>
              已拉黑 {{ monitorStore.config.blocked_sellers.length }} 个卖家:
            </n-text>
            <div style="margin-top: 12px">
              <n-space>
                <n-tag
                  v-for="seller in monitorStore.config.blocked_sellers"
                  :key="seller.seller_id"
                  type="error"
                  size="medium"
                  closable
                  :loading="removingBlockedSeller === seller.seller_id"
                  @close="removeBlockedSeller(seller.seller_id)"
                >
                  {{ seller.seller_name }}
                </n-tag>
              </n-space>
            </div>
          </div>

          <div v-else>
            <n-text depth="3">暂无拉黑卖家</n-text>
            <n-text
              depth="3"
              style="display: block; margin-top: 4px; font-size: 12px"
            >
              在商品详情弹窗中点击"拉黑商家"按钮可以添加
            </n-text>
          </div>
        </n-space>
      </n-card>

      <!-- 浏览器测试卡片 -->
      <n-card class="config-card" size="small">
        <template #header>
          <div class="card-header">
            <h3 class="section-title">浏览器测试</h3>
            <n-text depth="3" style="font-size: 12px">
              打开隔离浏览器窗口进行测试，可指定账号ID进行数据隔离
            </n-text>
          </div>
        </template>

        <n-space vertical size="medium">
          <div style="display: flex; align-items: center; gap: 12px">
            <n-text style="width: 80px; flex-shrink: 0">账号ID:</n-text>
            <n-input
              v-model:value="browserTestAccountId"
              placeholder="输入账号ID（可为空）"
              style="flex: 1; max-width: 300px"
              clearable
            />
            <n-button
              type="primary"
              @click="openTestBrowser"
              :loading="openingBrowser"
              :disabled="openingBrowser"
            >
              {{ openingBrowser ? "打开中..." : "打开浏览器" }}
            </n-button>
          </div>

          <n-alert type="info" style="margin-top: 12px">
            <template #header>使用说明</template>
            <div>
              <p>• 留空账号ID：打开默认浏览器窗口，无数据隔离</p>
              <p>• 指定账号ID：打开隔离浏览器窗口，数据独立存储</p>
              <p>• 关闭窗口时会自动获取并显示Cookie信息</p>
            </div>
          </n-alert>
        </n-space>
      </n-card>

      <!-- 应用更新卡片 -->
      <n-card class="config-card" size="small">
        <template #header>
          <div class="card-header">
            <h3 class="section-title">应用更新</h3>
            <n-text depth="3" style="font-size: 12px">
              检查并安装应用更新
            </n-text>
          </div>
        </template>

        <n-space direction="vertical" size="medium">
          <div class="update-info">
            <n-space align="center" justify="space-between">
              <div>
                <n-text depth="3" style="font-size: 12px">
                  {{ updateStatusText }}
                </n-text>
              </div>
              <UpdateNotification
                :show-check-button="true"
                :auto-check="false"
              />
            </n-space>
          </div>

          <n-alert type="info">
            <template #header>更新说明</template>
            <div>
              <p>• 应用会在启动时自动检查更新</p>
              <p>• 发现新版本时会弹出更新提示</p>
              <p>• 您也可以手动点击按钮检查更新</p>
            </div>
          </n-alert>
        </n-space>
      </n-card>

      <!-- 自动保存提示卡片 -->
      <n-card class="config-card" size="small">
        <div class="save-section">
          <n-alert type="info" :show-icon="false">
            配置修改后会自动保存，无需手动保存
          </n-alert>
        </div>
      </n-card>
    </div>

    <!-- 关键词价格配置弹窗 - 使用Naive UI组件 -->
    <n-modal
      v-model:show="showKeywordsPriceModal"
      preset="card"
      title="配置搜索关键词 & 价格范围"
      style="width: 90%; max-width: 800px"
      :segmented="true"
    >
      <div style="padding: 20px">
        <n-text depth="3">
          为每个搜索关键词设置独立的价格范围，支持多个关键词同时监控
        </n-text>

        <!-- 规则列表 - 使用Naive UI组件 -->
        <div
          v-for="(rule, index) in modalKeywordsPriceRules"
          :key="index"
          style="
            margin: 12px 0;
            padding: 12px;
            background: var(--n-color-embedded);
            border-radius: 6px;
          "
        >
          <div
            style="
              display: flex;
              align-items: center;
              gap: 10px;
              flex-wrap: wrap;
            "
          >
            <n-text style="width: 60px; flex-shrink: 0"
              >规则 {{ index + 1 }}</n-text
            >
            <n-input
              v-model:value="rule.keyword"
              placeholder="搜索关键词"
              style="flex: 1; min-width: 120px"
            />
            <n-input-number
              v-model:value="rule.min_price"
              :min="0"
              placeholder="最低价"
              style="width: 100px; flex-shrink: 0"
            />
            <n-text>-</n-text>
            <n-input-number
              v-model:value="rule.max_price"
              :min="0"
              placeholder="最高价"
              style="width: 100px; flex-shrink: 0"
            />
            <n-button
              @click="removeRule(index)"
              type="error"
              size="small"
              ghost
              :disabled="modalKeywordsPriceRules.length <= 1"
              style="flex-shrink: 0"
            >
              删除
            </n-button>
          </div>
        </div>

        <!-- 按钮组 -->
        <div
          style="margin: 20px 0; display: flex; justify-content: space-between"
        >
          <n-button @click="addNewRule" type="primary" ghost>
            添加新规则
          </n-button>
          <div style="display: flex; gap: 10px">
            <n-button @click="cancelKeywordsPrice" ghost> 取消 </n-button>
            <n-button @click="confirmKeywordsPrice" type="primary">
              确认
            </n-button>
          </div>
        </div>
      </div>
    </n-modal>

    <!-- 钉钉推送配置弹窗 -->
    <n-modal
      v-model:show="showDingtalkModal"
      preset="card"
      title="配置钉钉推送"
      style="width: 90%; max-width: 600px"
      :segmented="true"
    >
      <div style="padding: 20px">
        <n-text depth="3">
          配置钉钉群机器人Webhook URL，发现新商品时自动推送通知到钉钉群
        </n-text>

        <n-alert type="info" style="margin: 16px 0">
          <template #header>使用说明</template>
          <div>
            <p>1. 在钉钉群中添加自定义机器人</p>
            <p>2. 复制Webhook URL并粘贴到下方</p>
            <p>3. 注意：每个Hook每分钟最多发送20条消息</p>
          </div>
        </n-alert>

        <!-- Hook列表 -->
        <div style="margin: 20px 0">
          <div
            v-for="(hook, index) in modalDingtalkHooks"
            :key="index"
            style="
              margin: 12px 0;
              display: flex;
              align-items: center;
              gap: 10px;
            "
          >
            <n-text style="width: 60px; flex-shrink: 0">
              Hook {{ index + 1 }}
            </n-text>
            <n-input
              v-model:value="modalDingtalkHooks[index]"
              placeholder="https://oapi.dingtalk.com/robot/send?access_token=..."
              style="flex: 1"
            />
            <n-button
              type="error"
              ghost
              @click="removeModalDingtalkHook(index)"
              :disabled="modalDingtalkHooks.length <= 1"
            >
              删除
            </n-button>
          </div>
        </div>

        <!-- 按钮组 -->
        <div
          style="margin: 20px 0; display: flex; justify-content: space-between"
        >
          <n-button @click="addDingtalkHook" type="primary" ghost>
            添加Hook
          </n-button>
          <div style="display: flex; gap: 10px">
            <n-button @click="cancelDingtalkConfig" ghost> 取消 </n-button>
            <n-button @click="confirmDingtalkConfig" type="primary">
              确认
            </n-button>
          </div>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { safeInvoke } from "../utils/tauri";
import { createDiscreteApi } from "naive-ui";
import { useMonitorStore } from "../stores/monitor";
import { PROVINCE_OPTIONS, getProvinceById } from "../constants/provinces";
import UpdateNotification from "./UpdateNotification.vue";
import { updateState, UpdateStatus } from "../services/updater";

// 使用Naive UI的离散API
const { message } = createDiscreteApi(["message"]);
const monitorStore = useMonitorStore();

// 使用 store 中的状态和本地状态
// const isLoading = monitorStore.isLoading;     // ❌ 这样会失去响应式
// const settingsConfig = monitorStore.config;   // ❌ 这样会失去响应式
// 直接在模板中使用 monitorStore.config 的属性，不需要 computed 包装
// 因为配置项需要是可编辑的，computed 会使其变为只读

// 本地状态
const showKeywordsPriceModal = ref(false);
const modalKeywordsPriceRules = ref([]);
const isAutoSyncing = ref(false);
const removingBlockedSeller = ref(null); // 正在移除的卖家ID

// 浏览器测试相关状态
const browserTestAccountId = ref("");
const openingBrowser = ref(false);

// 省份选择相关状态
const provinceOptions = ref(PROVINCE_OPTIONS);
const selectedProvinceIds = computed({
  get: () => monitorStore.config.selected_provinces.map((p) => p.id),
  set: (ids) => {
    // 这个setter会在onProvinceSelectionChange中处理
  },
});

// 更新状态相关
const updateStatusText = computed(() => {
  switch (updateState.status) {
    case UpdateStatus.CHECKING:
      return "正在检查更新...";
    case UpdateStatus.UP_TO_DATE:
      return "当前已是最新版本";
    case UpdateStatus.AVAILABLE:
      return `发现新版本 ${updateState.version}`;
    case UpdateStatus.DOWNLOADING:
      return `正在下载更新... ${Math.round(updateState.progress)}%`;
    case UpdateStatus.INSTALLING:
      return "正在安装更新...";
    case UpdateStatus.READY_TO_RESTART:
      return "更新已安装，可以重启应用";
    case UpdateStatus.ERROR:
      return `更新失败: ${updateState.error}`;
    default:
      return "点击右侧按钮检查更新";
  }
});

// 配置现在自动保存，不需要手动保存函数

// 直接定义关键词价格规则相关函数
const openKeywordsPriceModal = () => {
  modalKeywordsPriceRules.value = [...monitorStore.config.keywords_price_rules];
  showKeywordsPriceModal.value = true;
};

const addNewRule = () => {
  modalKeywordsPriceRules.value.push({
    keyword: "",
    min_price: 0,
    max_price: 99999,
  });
};

const removeRule = (index) => {
  modalKeywordsPriceRules.value.splice(index, 1);
};

const confirmKeywordsPrice = () => {
  monitorStore.config.keywords_price_rules = [...modalKeywordsPriceRules.value];
  showKeywordsPriceModal.value = false;
  message.success("关键词价格规则已更新");
  monitorStore.debouncedSaveConfig();
};

const cancelKeywordsPrice = () => {
  showKeywordsPriceModal.value = false;
};

// 加载配置数据 - 使用 store 的方法
const loadConfig = async () => {
  try {
    await monitorStore.loadConfig();
  } catch (error) {
    console.error("加载配置失败:", error);
    message.error("加载配置失败: " + error);
  }
};

// 省份选择相关方法
const onProvinceSelectionChange = (selectedIds) => {
  // 根据选中的ID获取完整的省份信息
  const selectedProvinces = selectedIds.map((id) => {
    const province = getProvinceById(id);
    return {
      id: province.id,
      name: province.na,
      level: province.level,
      has_child: province.hasChild,
    };
  });

  // 更新配置
  monitorStore.config.selected_provinces = selectedProvinces;
  monitorStore.debouncedSaveConfig();
};

const removeProvince = (provinceId) => {
  monitorStore.config.selected_provinces =
    monitorStore.config.selected_provinces.filter((p) => p.id !== provinceId);
  monitorStore.debouncedSaveConfig();
};

// 移除拉黑卖家
const removeBlockedSeller = async (sellerId) => {
  try {
    removingBlockedSeller.value = sellerId;

    // 获取当前配置
    const currentConfig = { ...monitorStore.config };

    // 确保 blocked_sellers 字段存在（兼容老版本）
    if (!currentConfig.blocked_sellers) {
      currentConfig.blocked_sellers = [];
    }

    // 移除指定卖家
    currentConfig.blocked_sellers = currentConfig.blocked_sellers.filter(
      (seller) => seller.seller_id !== sellerId
    );

    // 转换为后端期望的格式（确保字段名正确）
    const backendConfig = {
      interval_seconds: currentConfig.interval_seconds,
      target_page_count: currentConfig.target_page_count,
      keywords: currentConfig.keywords,
      exclude_keywords: currentConfig.exclude_keywords,
      min_price: currentConfig.min_price,
      max_price: currentConfig.max_price,
      notify_enabled: currentConfig.notify_enabled,
      keyword_price_rules: currentConfig.keywords_price_rules || [], // 注意字段名转换
      dingtalk_hooks: currentConfig.dingtalk_hooks,
      display_limit: currentConfig.display_limit,
      blocked_sellers: currentConfig.blocked_sellers || [], // 拉黑卖家列表
      selected_provinces: currentConfig.selected_provinces || [], // 选中的省份列表
    };

    // 使用现有的配置更新命令
    await safeInvoke("config_update", { config: backendConfig });

    message.success("已移除拉黑卖家");

    // 重新加载配置以更新界面
    await monitorStore.loadConfig();
  } catch (error) {
    console.error("移除拉黑卖家失败:", error);
    message.error("移除拉黑卖家失败: " + error);
  } finally {
    removingBlockedSeller.value = null;
  }
};

// 组件挂载时加载配置
onMounted(() => {
  loadConfig();
});

// 钉钉推送相关数据
const showDingtalkModal = ref(false);
const modalDingtalkHooks = ref([]);
const testingHooks = ref(false);

// 钉钉推送相关方法
function openDingtalkModal() {
  modalDingtalkHooks.value =
    monitorStore.config.dingtalk_hooks.length > 0
      ? [...monitorStore.config.dingtalk_hooks]
      : [""];
  showDingtalkModal.value = true;
}

function addDingtalkHook() {
  modalDingtalkHooks.value.push("");
}

function removeDingtalkHook(index) {
  monitorStore.config.dingtalk_hooks.splice(index, 1);
  monitorStore.debouncedSaveConfig();
}

function removeModalDingtalkHook(index) {
  modalDingtalkHooks.value.splice(index, 1);
}

function confirmDingtalkConfig() {
  const validHooks = modalDingtalkHooks.value.filter(
    (hook) => hook && hook.trim()
  );
  monitorStore.config.dingtalk_hooks = validHooks;
  showDingtalkModal.value = false;
  message.success("钉钉推送配置已更新");
  monitorStore.debouncedSaveConfig();
}

function cancelDingtalkConfig() {
  showDingtalkModal.value = false;
}

// 保存钉钉推送开关状态
async function saveDingtalkEnabled(enabled) {
  try {
    await monitorStore.saveConfig();
    message.success(enabled ? "钉钉推送已开启" : "钉钉推送已关闭");
  } catch (error) {
    console.error("保存钉钉推送开关失败:", error);
    message.error("保存失败: " + error);
  }
}

async function testAllDingtalkHooks() {
  if (monitorStore.config.dingtalk_hooks.length === 0) {
    message.warning("请先配置钉钉Hook");
    return;
  }

  testingHooks.value = true;
  try {
    const results = await safeInvoke("test_all_dingtalk_hooks", {
      hookUrls: monitorStore.config.dingtalk_hooks,
    });

    let successCount = 0;
    let failCount = 0;

    results.forEach(([hook, status]) => {
      if (status === "成功") {
        successCount++;
      } else {
        failCount++;
      }
    });

    if (failCount === 0) {
      message.success(`所有 ${successCount} 个Hook测试成功！`);
    } else {
      message.warning(
        `${successCount} 个成功，${failCount} 个失败，请检查失败的Hook配置`
      );
    }
  } catch (error) {
    console.error("测试钉钉Hook失败:", error);
    message.error("测试失败: " + error);
  } finally {
    testingHooks.value = false;
  }
}

// 浏览器测试方法
async function openTestBrowser() {
  if (openingBrowser.value) return;

  openingBrowser.value = true;
  try {
    const accountId = browserTestAccountId.value.trim();
    const result = await safeInvoke("open_isolated_browser", {
      accountId: accountId,
    });

    message.success(result || "浏览器窗口已打开");
  } catch (error) {
    console.error("打开浏览器失败:", error);
    message.error("打开浏览器失败: " + error);
  } finally {
    openingBrowser.value = false;
  }
}

// 不再需要格式化同步时间函数，因为配置自动保存
</script>

<style scoped>
.page {
  margin: 0 auto;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 12px;
}

.page-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color);
  line-height: 1.2;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
}

.config-card {
  margin-bottom: 0;
  box-shadow: var(--n-box-shadow-1);
  border: 1px solid var(--n-border-color);
}

.config-card:hover {
  box-shadow: var(--n-box-shadow-2);
  transition: box-shadow 0.3s ease;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color);
  flex-shrink: 0;
}

/* 修复卡片内容样式 */
.main-container :deep(.n-card__content) {
  padding: 20px;
}

.main-container :deep(.n-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--n-border-color);
}

.cookie-input-container {
  position: relative;
}

.cookie-input {
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier,
    monospace !important;
  font-size: 12px !important;
  line-height: 1.4 !important;
}

.rules-preview {
  padding: 16px;
  background: var(--n-color-embedded);
  border-radius: 8px;
  border: 1px solid var(--n-border-color);
}

.rule-tag {
  margin: 2px 0;
  font-weight: 500;
}

.param-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-label {
  font-weight: 500;
  color: var(--n-text-color);
  font-size: 14px;
}

.notification-setting {
  padding: 0;
}

.rule-config {
  padding: 12px;
  background: var(--n-color-embedded);
  border-radius: 6px;
  margin-bottom: 12px;
}

.save-section {
  text-align: center;
  padding: 0;
  margin: 0;
}

/* 滚动条美化 */
.main-container::-webkit-scrollbar {
  width: 8px;
}

.main-container::-webkit-scrollbar-track {
  background: var(--n-scrollbar-color);
  border-radius: 4px;
}

.main-container::-webkit-scrollbar-thumb {
  background: var(--n-scrollbar-color-hover);
  border-radius: 4px;
}

.main-container::-webkit-scrollbar-thumb:hover {
  background: var(--n-scrollbar-color-pressed);
}

@media (max-width: 768px) {
  .page {
    max-width: 100%;
    height: 100%;
  }

  .page-title {
    font-size: 18px;
  }

  .main-container :deep(.n-card__content) {
    padding: 16px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .config-card {
    padding: 16px 0;
  }

  .param-item {
    gap: 6px;
  }

  .param-label {
    font-size: 13px;
  }

  .rule-config .n-space {
    flex-wrap: wrap;
  }

  .rule-config .n-input,
  .rule-config .n-input-number {
    min-width: 80px !important;
  }
}

/* 省份选择相关样式 */
.province-selector {
  width: 100%;
  min-width: 200px;
}

.selected-provinces-preview {
  background: var(--n-color-embedded);
  padding: 12px;
  border-radius: 6px;
  border: 1px solid var(--n-border-color);
}

.selected-provinces-preview .n-tag {
  margin: 2px;
}
</style> 