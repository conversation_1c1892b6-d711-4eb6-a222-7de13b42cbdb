import { safeInvoke, safeListen } from '../utils/tauri'
import { ref, reactive } from 'vue'

// 更新状态枚举
export const UpdateStatus = {
  CHECKING: 'Checking',
  AVAILABLE: 'Available',
  UP_TO_DATE: 'UpToDate',
  DOWNLOADING: 'Downloading',
  INSTALLING: 'Installing',
  READY_TO_RESTART: 'ReadyToRestart',
  ERROR: 'Error'
}

// 更新状态响应式数据
export const updateState = reactive({
  status: UpdateStatus.UP_TO_DATE,
  version: '',
  notes: '',
  progress: 0,
  error: ''
})

// 是否正在检查更新
export const isCheckingUpdate = ref(false)

// 是否有可用更新
export const hasUpdate = ref(false)

// 是否正在下载
export const isDownloading = ref(false)

// 是否准备重启
export const isReadyToRestart = ref(false)

/**
 * 更新服务类
 */
export class UpdaterService {
  constructor() {
    this.listeners = []
    this.initEventListeners()
  }

  /**
   * 初始化事件监听器
   */
  async initEventListeners() {
    try {
      // 监听更新状态变化事件
      const unlisten = await safeListen('updater_status_changed', (event) => {
        console.log('📡 收到更新状态变化事件:', event.payload)
        this.handleStatusUpdate(event.payload)
      })

      if (unlisten) {
        this.listeners.push(unlisten)
      }
    } catch (error) {
      console.error('❌ 初始化更新事件监听器失败:', error)
    }
  }

  /**
   * 处理状态更新
   */
  handleStatusUpdate(status) {
    console.log('🔄 处理更新状态:', status)

    // 重置所有状态
    isCheckingUpdate.value = false
    hasUpdate.value = false
    isDownloading.value = false
    isReadyToRestart.value = false

    if (typeof status === 'string') {
      updateState.status = status

      switch (status) {
        case UpdateStatus.CHECKING:
          isCheckingUpdate.value = true
          break
        case UpdateStatus.UP_TO_DATE:
          // 已是最新版本
          break
        case UpdateStatus.INSTALLING:
          // 安装中
          break
        case UpdateStatus.READY_TO_RESTART:
          isReadyToRestart.value = true
          break
      }
    } else if (typeof status === 'object') {
      // 处理复杂状态对象
      if (status.Available) {
        updateState.status = UpdateStatus.AVAILABLE
        updateState.version = status.Available.version || ''
        updateState.notes = status.Available.notes || ''
        hasUpdate.value = true
      } else if (status.Downloading) {
        updateState.status = UpdateStatus.DOWNLOADING
        updateState.progress = status.Downloading.progress || 0
        isDownloading.value = true
      } else if (status.Error) {
        updateState.status = UpdateStatus.ERROR
        updateState.error = status.Error.message || '未知错误'
      }
    }
  }

  /**
   * 检查更新
   */
  async checkForUpdates() {
    try {
      console.log('🔍 开始检查更新...')
      isCheckingUpdate.value = true

      const result = await safeInvoke('updater_check')
      console.log('✅ 检查更新结果:', result)

      this.handleStatusUpdate(result)
      return result
    } catch (error) {
      console.error('❌ 检查更新失败:', error)
      updateState.status = UpdateStatus.ERROR
      updateState.error = error.message || '检查更新失败'
      isCheckingUpdate.value = false
      throw error
    }
  }

  /**
   * 下载并安装更新
   */
  async downloadAndInstall() {
    try {
      console.log('📥 开始下载并安装更新...')
      isDownloading.value = true

      await safeInvoke('updater_download_and_install')
      console.log('✅ 更新下载安装完成')

    } catch (error) {
      console.error('❌ 下载安装更新失败:', error)
      updateState.status = UpdateStatus.ERROR
      updateState.error = error.message || '下载安装更新失败'
      isDownloading.value = false
      throw error
    }
  }

  /**
   * 重启应用
   */
  async restartApp() {
    try {
      console.log('🔄 重启应用...')
      await safeInvoke('updater_restart')
      console.log('✅ 应用重启成功')
    } catch (error) {
      console.error('❌ 重启应用失败:', error)
      throw error
    }
  }

  /**
   * 获取当前更新状态
   */
  async getStatus() {
    try {
      const status = await safeInvoke('updater_get_status')
      this.handleStatusUpdate(status)
      return status
    } catch (error) {
      console.error('❌ 获取更新状态失败:', error)
      throw error
    }
  }

  /**
   * 清理事件监听器
   */
  cleanup() {
    this.listeners.forEach(unlisten => {
      if (typeof unlisten === 'function') {
        unlisten()
      }
    })
    this.listeners = []
  }
}

// 创建全局更新服务实例
export const updaterService = new UpdaterService()

// 便捷方法
export const checkForUpdates = () => updaterService.checkForUpdates()
export const downloadAndInstall = () => updaterService.downloadAndInstall()
export const restartApp = () => updaterService.restartApp()
export const getUpdateStatus = () => updaterService.getStatus()

// 自动检查更新（应用启动时调用）
export const autoCheckForUpdates = async () => {
  try {
    console.log('🚀 应用启动时自动检查更新...')
    await checkForUpdates()
  } catch (error) {
    console.warn('⚠️ 自动检查更新失败，将在后台静默处理:', error)
  }
}
