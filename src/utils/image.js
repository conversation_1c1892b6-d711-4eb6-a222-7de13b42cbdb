/**
 * 图片URL处理工具
 */

/**
 * 将HTTP图片URL转换为HTTPS
 * @param {string} url - 原始图片URL
 * @returns {string} - 转换后的HTTPS URL
 */
export function convertToHttps(url) {
  if (!url || typeof url !== 'string') {
    return url;
  }
  
  // 如果已经是HTTPS或者不是HTTP协议，直接返回
  if (!url.startsWith('http://')) {
    return url;
  }
  
  // 将http://替换为https://
  return url.replace('http://', 'https://');
}

/**
 * 处理头像URL，确保使用HTTPS协议
 * @param {string} avatarUrl - 头像URL
 * @returns {string} - 处理后的头像URL
 */
export function processAvatarUrl(avatarUrl) {
  return convertToHttps(avatarUrl);
}

/**
 * 处理商品图片URL，确保使用HTTPS协议
 * @param {string} picUrl - 商品图片URL
 * @returns {string} - 处理后的图片URL
 */
export function processProductImageUrl(picUrl) {
  return convertToHttps(picUrl);
}

/**
 * 批量处理商品数据中的图片URL
 * @param {Array} items - 商品数据数组
 * @returns {Array} - 处理后的商品数据数组
 */
export function processItemsImageUrls(items) {
  if (!Array.isArray(items)) {
    return items;
  }
  
  return items.map(item => ({
    ...item,
    pic_url: processProductImageUrl(item.pic_url)
  }));
}

/**
 * 处理账号数据中的头像URL
 * @param {Array} accounts - 账号数据数组
 * @returns {Array} - 处理后的账号数据数组
 */
export function processAccountsAvatarUrls(accounts) {
  if (!Array.isArray(accounts)) {
    return accounts;
  }
  
  return accounts.map(account => {
    const avatar = account.metadata?.avatar;
    if (avatar) {
      return {
        ...account,
        metadata: {
          ...account.metadata,
          avatar: processAvatarUrl(avatar)
        }
      };
    }
    return account;
  });
}

/**
 * 默认头像SVG（Base64编码）
 */
export const DEFAULT_AVATAR = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNmNWY1ZjUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iI2Q5ZDlkOSIvPgo8cGF0aCBkPSJNMTAgMzJjMC02IDQtMTAgMTAtMTBzMTAgNCAxMCAxMCIgZmlsbD0iI2Q5ZDlkOSIvPgo8L3N2Zz4K";
