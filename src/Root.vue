<template>
  <div class="app-container">
    <n-config-provider :theme="theme">
      <n-dialog-provider>
        <n-message-provider>
          <n-notification-provider placement="bottom-right">
            <NotificationSetup />
            <!-- 已注释：启动时自动检查更新 -->
            <!-- <UpdateNotification :auto-check="true" /> -->
          </n-notification-provider>
        </n-message-provider>
      </n-dialog-provider>
    </n-config-provider>
  </div>
</template>

<script setup>
import { onMounted, provide, computed } from "vue";
// eslint-disable-next-line no-unused-vars
import {
  NDialogProvider,
  NMessageProvider,
  NNotificationProvider,
  NConfigProvider,
  darkTheme,
  lightTheme,
} from "naive-ui";
// 移除了不需要的 safeListen 和 getCurrentWebviewWindow 导入
import { useAppState } from "./composables/useAppState";
import { useThemeStore } from "./stores/theme";
import NotificationSetup from "./components/NotificationSetup.vue";
// import UpdateNotification from "./components/UpdateNotification.vue";

// 应用状态管理
const appState = useAppState();

// 主题管理
const themeStore = useThemeStore();
const theme = computed(() => (themeStore.isDark ? darkTheme : lightTheme));

// 提供应用状态给子组件
Object.keys(appState).forEach((key) => {
  provide(key, appState[key]);
});

// 窗口关闭监听器相关代码已移除，使用 Tauri 默认关闭行为

// 在mounted钩子中初始化
onMounted(async () => {
  // 初始化主题
  themeStore.initializeTheme();

  // 初始化应用状态
  await appState.initialize();
});

// 组件卸载时清理（已移除窗口监听器相关代码）
</script>

<style>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  overflow: hidden;
  background-color: var(--n-base-color);
}

.app-container {
  height: 100vh;
  width: 100vw;
  background-color: var(--n-base-color);
}
</style>
