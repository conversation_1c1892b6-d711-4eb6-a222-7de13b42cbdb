{"$schema": "https://schema.tauri.app/config/2", "productName": "goldfish-app", "version": "0.1.2", "identifier": "com.goldfish-app.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1430", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"withGlobalTauri": true, "windows": [], "security": {"csp": {"default-src": "'self' https://*.goofish.com https://h5api.m.goofish.com https://acs.m.goofish.com", "connect-src": "'self' https://*.goofish.com https://h5api.m.goofish.com https://acs.m.goofish.com ipc: http://ipc.localhost", "frame-src": "'self' https://*.goofish.com https://h5api.m.goofish.com https://acs.m.goofish.com", "script-src": "'self' 'unsafe-inline' https://*.goofish.com", "style-src": "'self' 'unsafe-inline' https://*.goofish.com", "img-src": "'self' data: blob: https://*.goofish.com https://h5api.m.goofish.com https://*.alicdn.com https://img.alicdn.com http://*.alicdn.com http://img.alicdn.com https://*.aliyuncs.com http://*.aliyuncs.com https://gw.alicdn.com http://gw.alicdn.com https://ae01.alicdn.com http://ae01.alicdn.com"}}}, "bundle": {"active": true, "targets": ["nsis", "app"], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "macOS": {"entitlements": null, "exceptionDomain": null, "frameworks": [], "providerShortName": null, "signingIdentity": null}}}