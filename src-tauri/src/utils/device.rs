// 设备相关工具函数

/// 获取系统主机名
pub fn get_hostname() -> String {
    hostname::get()
        .unwrap_or_else(|_| std::ffi::OsString::from("unknown"))
        .to_string_lossy()
        .to_string()
}

/// 检查是否为虚拟机环境
pub fn is_virtual_machine() -> bool {
    // 简化实现，检查常见的虚拟机标识
    let hostname = get_hostname().to_lowercase();
    hostname.contains("vm")
        || hostname.contains("virtual")
        || hostname.contains("vbox")
        || hostname.contains("vmware")
}

/// 获取操作系统信息
pub fn get_os_info() -> String {
    std::env::consts::OS.to_string()
}

/// 获取架构信息
pub fn get_arch_info() -> String {
    std::env::consts::ARCH.to_string()
}
