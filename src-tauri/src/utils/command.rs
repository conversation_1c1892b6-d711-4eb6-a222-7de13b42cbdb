use std::process::Command;

#[cfg(target_os = "windows")]
use std::os::windows::process::CommandExt;

/// 执行命令并隐藏窗口（在Windows上）
pub fn execute_command(program: &str, args: &[&str]) -> std::io::Result<std::process::Output> {
    let mut cmd = Command::new(program);
    cmd.args(args);

    #[cfg(target_os = "windows")]
    {
        const CREATE_NO_WINDOW: u32 = 0x08000000;
        cmd.creation_flags(CREATE_NO_WINDOW);
    }

    cmd.output()
}

/// 执行命令并返回字符串结果
pub fn execute_command_string(program: &str, args: &[&str]) -> Result<String, String> {
    let output = execute_command(program, args).map_err(|e| format!("执行命令失败: {}", e))?;

    if output.status.success() {
        String::from_utf8(output.stdout).map_err(|e| format!("解析命令输出失败: {}", e))
    } else {
        let stderr = String::from_utf8_lossy(&output.stderr);
        Err(format!("命令执行失败: {}", stderr))
    }
}

/// 检查命令是否可用
pub fn is_command_available(program: &str) -> bool {
    execute_command(program, &["--version"]).is_ok()
        || execute_command(program, &["-v"]).is_ok()
        || execute_command(program, &["--help"]).is_ok()
}
