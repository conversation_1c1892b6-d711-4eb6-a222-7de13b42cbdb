use std::net::{IpAddr, Ipv4Addr};

/// 检查IP地址是否为本地地址
pub fn is_local_ip(ip: &IpAddr) -> bool {
    match ip {
        IpAddr::V4(ipv4) => ipv4.is_loopback() || ipv4.is_private() || ipv4.is_link_local(),
        IpAddr::V6(ipv6) => ipv6.is_loopback(),
    }
}

/// 检查端口是否可用
pub fn is_port_available(port: u16) -> bool {
    std::net::TcpListener::bind(("127.0.0.1", port)).is_ok()
}

/// 获取可用端口
pub fn get_available_port(start_port: u16) -> Option<u16> {
    for port in start_port..65535 {
        if is_port_available(port) {
            return Some(port);
        }
    }
    None
}

/// 验证URL格式
pub fn is_valid_url(url: &str) -> bool {
    url::Url::parse(url).is_ok()
}

/// 提取域名
pub fn extract_domain(url: &str) -> Option<String> {
    url::Url::parse(url)
        .ok()
        .and_then(|parsed| parsed.host_str().map(|s| s.to_string()))
}
