use crate::core::{get_sign, APP_KEY, J<PERSON>};
use std::time::{SystemTime, UNIX_EPOCH};

/// 统一的加密服务，处理所有API请求的签名和参数生成
#[derive(Clone)]
pub struct CryptoService {}

impl CryptoService {
    pub fn new() -> Self {
        Self {}
    }

    /// 从Cookie字符串中提取指定字段的值
    pub fn extract_cookie_field(&self, cookies: &str, field: &str) -> Option<String> {
        cookies
            .split(";")
            .find(|s| s.trim().starts_with(&format!("{}=", field)))
            .and_then(|s| s.split("=").nth(1))
            .map(|s| s.trim().to_string())
    }

    /// 提取 _m_h5_tk token的第一部分（用于签名）
    pub fn extract_token_for_signature(&self, cookies: &str) -> String {
        let full_token = self
            .extract_cookie_field(cookies, "_m_h5_tk")
            .unwrap_or_default();
        full_token.split("_").nth(0).unwrap_or("").to_string()
    }

    /// 生成当前时间戳（毫秒）
    pub fn generate_timestamp(&self) -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64
    }

    /// 生成API请求签名
    pub async fn generate_signature(
        &self,
        token: &str,
        timestamp: u64,
        data: &str,
    ) -> Result<String, String> {
        let sign_input = format!("{}&{}&{}&{}", token, timestamp, APP_KEY, data);

        let signature = get_sign(&sign_input);

        Ok(signature)
    }

    /// 构建标准查询参数
    pub fn build_query_params(
        &self,
        timestamp: u64,
        additional: &[(String, String)],
    ) -> Vec<(String, String)> {
        let mut params = vec![
            ("t".to_string(), timestamp.to_string()),
            ("jsv".to_string(), JSV.to_string()),
            ("appKey".to_string(), APP_KEY.to_string()),
        ];

        // 添加额外参数
        for (key, value) in additional {
            params.push((key.clone(), value.clone()));
        }

        params
    }

    /// 验证和标准化请求数据
    pub async fn prepare_request_data(&self, data: &serde_json::Value) -> Result<String, String> {
        let data_string =
            serde_json::to_string(data).map_err(|e| format!("序列化请求数据失败: {}", e))?;

        Ok(data_string)
    }

    /// 获取应用程序密钥
    pub fn get_app_key(&self) -> u32 {
        APP_KEY
    }

    /// 获取JavaScript版本
    pub fn get_jsv(&self) -> &'static str {
        JSV
    }

    /// 验证Cookie格式
    pub async fn validate_cookies(&self, cookies: &str) -> Result<bool, String> {
        if cookies.trim().is_empty() {
            return Ok(false);
        }

        let has_required_token = self.extract_cookie_field(cookies, "_m_h5_tk").is_some();

        if !has_required_token {
            return Ok(false);
        }

        Ok(true)
    }
}

// #[cfg(test)]
// mod tests {
//     use super::*;

//     #[tokio::test]
//     async fn test_extract_cookie_field() {
//         // 测试代码暂时注释掉
//     }
//
//     #[tokio::test]
//     async fn test_generate_signature() {
//         // 测试代码暂时注释掉
//     }
// }
