use super::service::{MonitorService, MonitorStatus};
use crate::models::config::{MonitorConfig, MonitorItem};
use tauri::{AppHandle, Emitter};

/// 监控事件处理器
pub struct MonitorEventHandler {
    monitor_service: MonitorService,
}

impl MonitorEventHandler {
    pub fn new(monitor_service: MonitorService) -> Self {
        Self { monitor_service }
    }

    /// 发送监控状态变化事件
    pub async fn emit_status_changed(&self, status: &MonitorStatus) -> Result<(), String> {
        self.monitor_service
            .app_handle()
            .emit("monitor_status_changed", status)
            .map_err(|e| format!("发送监控状态变化事件失败: {}", e))
    }

    /// 发送新商品事件
    pub async fn emit_new_item(&self, item: &MonitorItem) -> Result<(), String> {
        self.monitor_service
            .app_handle()
            .emit("monitor_new_item", item)
            .map_err(|e| format!("发送新商品事件失败: {}", e))
    }

    /// 发送数据清空事件
    pub async fn emit_data_cleared(&self) -> Result<(), String> {
        self.monitor_service
            .app_handle()
            .emit("monitor_data_cleared", ())
            .map_err(|e| format!("发送数据清空事件失败: {}", e))
    }

    /// 发送配置更新事件
    pub async fn emit_config_updated(&self, config: &MonitorConfig) -> Result<(), String> {
        self.monitor_service
            .app_handle()
            .emit("monitor_config_updated", config)
            .map_err(|e| format!("发送配置更新事件失败: {}", e))
    }

    /// 发送监控错误事件
    pub async fn emit_error(&self, error: &str) -> Result<(), String> {
        self.monitor_service
            .app_handle()
            .emit("monitor_error", error)
            .map_err(|e| format!("发送监控错误事件失败: {}", e))
    }
}

/// 监控事件监听器设置
pub async fn setup_monitor_listeners(app_handle: AppHandle, monitor_service: MonitorService) {
    let handler = MonitorEventHandler::new(monitor_service.clone());

    // 监听监控相关事件
    let app_handle_clone = app_handle.clone();
    let monitor_service_clone = monitor_service.clone();

    tokio::spawn(async move {
        // 这里可以设置监控事件监听器
        // 例如监听数据变化、状态变化等
    });
}

/// 便捷的监控事件发送函数
pub async fn emit_monitor_event(
    app_handle: &AppHandle,
    event_name: &str,
    payload: &serde_json::Value,
) -> Result<(), String> {
    app_handle
        .emit(event_name, payload)
        .map_err(|e| format!("发送监控事件失败: {}", e))
}

/// 发送监控统计事件
pub async fn emit_monitor_stats(
    app_handle: &AppHandle,
    stats: &serde_json::Value,
) -> Result<(), String> {
    app_handle
        .emit("monitor_stats", stats)
        .map_err(|e| format!("发送监控统计事件失败: {}", e))
}
