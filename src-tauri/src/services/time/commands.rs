use super::service::{TimeService, TimeSync};
use chrono::{DateTime, Local, Utc};
use serde::{Deserialize, Serialize};
use tauri::State;

/// 时间信息响应
#[derive(Debug, Serialize)]
pub struct TimeInfo {
    pub server_time: Option<String>,
    pub local_time: String,
    pub offset_seconds: Option<i64>,
    pub is_synced: bool,
    pub needs_resync: bool,
}

/// 获取时间信息
#[tauri::command]
pub async fn time_get_info(time_service: State<'_, TimeService>) -> Result<TimeInfo, String> {
    let time_sync = time_service.get_time_sync().await;
    let local_time = time_service.get_local_time().await;
    let needs_resync = time_service.needs_resync().await;

    let info = TimeInfo {
        server_time: time_sync.as_ref().map(|s| s.get_server_time().to_rfc3339()),
        local_time: local_time.to_rfc3339(),
        offset_seconds: time_sync.as_ref().map(|s| s.offset_seconds),
        is_synced: time_sync.is_some(),
        needs_resync,
    };

    Ok(info)
}

/// 手动同步服务器时间
#[tauri::command]
pub async fn time_sync_from_header(
    time_service: State<'_, TimeService>,
    date_header: String,
) -> Result<(), String> {
    time_service.update_from_response_header(&date_header).await
}

/// 获取当前服务器时间
#[tauri::command]
pub async fn time_get_server_time(
    time_service: State<'_, TimeService>,
) -> Result<Option<String>, String> {
    let server_time = time_service.get_server_time().await;
    Ok(server_time.map(|t| t.to_rfc3339()))
}

/// 获取时间偏移量
#[tauri::command]
pub async fn time_get_offset(time_service: State<'_, TimeService>) -> Result<Option<i64>, String> {
    Ok(time_service.get_time_offset().await)
}

/// 验证时间（用于安全验证）
#[tauri::command]
pub async fn time_validate(
    time_service: State<'_, TimeService>,
    target_time: String,
) -> Result<bool, String> {
    let target = DateTime::parse_from_rfc3339(&target_time)
        .map_err(|e| format!("时间格式错误: {}", e))?
        .with_timezone(&Utc);

    time_service.validate_time(target).await
}
