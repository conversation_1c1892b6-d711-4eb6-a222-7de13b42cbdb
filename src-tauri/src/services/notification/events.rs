use super::service::{NotificationMessage, NotificationService};
use tauri::{AppHandle, Emitter};

/// 通知事件处理器
pub struct NotificationEventHandler {
    notification_service: NotificationService,
}

impl NotificationEventHandler {
    pub fn new(notification_service: NotificationService) -> Self {
        Self {
            notification_service,
        }
    }

    /// 发送通知发送成功事件
    pub async fn emit_notification_sent(
        &self,
        channel_name: &str,
        message: &NotificationMessage,
    ) -> Result<(), String> {
        let payload = serde_json::json!({
            "channel": channel_name,
            "message": message,
            "timestamp": chrono::Utc::now().to_rfc3339()
        });

        self.notification_service
            .app_handle()
            .emit("notification_sent", &payload)
            .map_err(|e| format!("发送通知发送成功事件失败: {}", e))
    }

    /// 发送通知发送失败事件
    pub async fn emit_notification_failed(
        &self,
        channel_name: &str,
        error: &str,
    ) -> Result<(), String> {
        let payload = serde_json::json!({
            "channel": channel_name,
            "error": error,
            "timestamp": chrono::Utc::now().to_rfc3339()
        });

        self.notification_service
            .app_handle()
            .emit("notification_failed", &payload)
            .map_err(|e| format!("发送通知发送失败事件失败: {}", e))
    }

    /// 发送渠道添加事件
    pub async fn emit_channel_added(&self, channel_name: &str) -> Result<(), String> {
        self.notification_service
            .app_handle()
            .emit("notification_channel_added", channel_name)
            .map_err(|e| format!("发送渠道添加事件失败: {}", e))
    }

    /// 发送渠道移除事件
    pub async fn emit_channel_removed(&self, channel_name: &str) -> Result<(), String> {
        self.notification_service
            .app_handle()
            .emit("notification_channel_removed", channel_name)
            .map_err(|e| format!("发送渠道移除事件失败: {}", e))
    }
}

/// 通知事件监听器设置
pub async fn setup_notification_listeners(
    app_handle: AppHandle,
    notification_service: NotificationService,
) {
    let handler = NotificationEventHandler::new(notification_service.clone());

    // 监听通知相关事件
    let app_handle_clone = app_handle.clone();
    let notification_service_clone = notification_service.clone();

    tokio::spawn(async move {
        // 这里可以设置通知事件监听器
        // 例如监听通知发送状态、渠道状态等
    });
}

/// 便捷的通知事件发送函数
pub async fn emit_notification_event(
    app_handle: &AppHandle,
    event_name: &str,
    payload: &serde_json::Value,
) -> Result<(), String> {
    app_handle
        .emit(event_name, payload)
        .map_err(|e| format!("发送通知事件失败: {}", e))
}

/// 发送系统通知事件
pub async fn emit_system_notification(
    app_handle: &AppHandle,
    title: &str,
    message: &str,
) -> Result<(), String> {
    let payload = serde_json::json!({
        "title": title,
        "message": message,
        "timestamp": chrono::Utc::now().to_rfc3339()
    });

    app_handle
        .emit("system_notification", &payload)
        .map_err(|e| format!("发送系统通知事件失败: {}", e))
}
