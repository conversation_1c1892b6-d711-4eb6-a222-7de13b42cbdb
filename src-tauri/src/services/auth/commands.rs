use super::service::{ActivationInfo, AuthService, CookieInfo, DeviceInfo};
use std::collections::HashMap;
use tauri::State;

/// 获取设备信息
#[tauri::command]
pub async fn auth_get_device_info(
    auth_service: State<'_, AuthService>,
) -> Result<DeviceInfo, String> {
    auth_service.get_device_info().await
}

/// 生成设备指纹
#[tauri::command]
pub async fn auth_generate_device_fingerprint(
    auth_service: State<'_, AuthService>,
) -> Result<String, String> {
    auth_service.generate_device_fingerprint().await
}

/// 验证激活码
#[tauri::command]
pub async fn auth_validate_activation_code(
    auth_service: State<'_, AuthService>,
    activation_code: String,
) -> Result<serde_json::Value, String> {
    auth_service
        .validate_activation_code(&activation_code)
        .await
}

/// 检查激活状态
#[tauri::command]
pub async fn auth_check_activation_status(
    auth_service: State<'_, AuthService>,
) -> Result<bool, String> {
    auth_service.check_activation_status().await
}

/// 获取激活信息
#[tauri::command]
pub async fn auth_get_activation_info(
    auth_service: State<'_, AuthService>,
) -> Result<ActivationInfo, String> {
    Ok(auth_service.get_activation_info().await)
}

/// 设置Cookie
#[tauri::command]
pub async fn auth_set_cookie(
    auth_service: State<'_, AuthService>,
    name: String,
    cookie_info: CookieInfo,
) -> Result<(), String> {
    auth_service.set_cookie(&name, cookie_info).await
}

/// 获取Cookie
#[tauri::command]
pub async fn auth_get_cookie(
    auth_service: State<'_, AuthService>,
    name: String,
) -> Result<Option<CookieInfo>, String> {
    Ok(auth_service.get_cookie(&name).await)
}

/// 清除Cookie
#[tauri::command]
pub async fn auth_clear_cookie(
    auth_service: State<'_, AuthService>,
    name: String,
) -> Result<(), String> {
    auth_service.clear_cookie(&name).await
}

/// 清除所有Cookie
#[tauri::command]
pub async fn auth_clear_all_cookies(auth_service: State<'_, AuthService>) -> Result<(), String> {
    auth_service.clear_all_cookies().await
}

/// 检查Cookie是否存在
#[tauri::command]
pub async fn auth_has_cookie(
    auth_service: State<'_, AuthService>,
    name: String,
) -> Result<bool, String> {
    Ok(auth_service.has_cookie(&name).await)
}

/// 获取所有Cookie
#[tauri::command]
pub async fn auth_get_all_cookies(
    auth_service: State<'_, AuthService>,
) -> Result<HashMap<String, CookieInfo>, String> {
    Ok(auth_service.get_all_cookies().await)
}
