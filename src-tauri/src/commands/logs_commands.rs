use std::path::PathBuf;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};

/// 获取日志文件路径
#[tauri::command]
pub async fn logs_get_file_path(app_handle: AppHandle) -> Result<String, String> {
    // 获取日志目录路径
    let log_dir = app_handle
        .path()
        .app_log_dir()
        .map_err(|e| format!("获取日志目录失败: {}", e))?;

    // 查找最新的日志文件
    let log_files = std::fs::read_dir(&log_dir)
        .map_err(|e| format!("读取日志目录失败: {}", e))?
        .filter_map(|entry| {
            let entry = entry.ok()?;
            let path = entry.path();
            if path.is_file() && path.extension()?.to_str()? == "log" {
                Some(path)
            } else {
                None
            }
        })
        .collect::<Vec<PathBuf>>();

    if log_files.is_empty() {
        // 如果没有日志文件，返回默认的日志文件路径
        let default_log_file = log_dir.join("goldfish-app.log");
        Ok(default_log_file.to_string_lossy().to_string())
    } else {
        // 返回最新的日志文件
        let latest_log = log_files
            .into_iter()
            .max_by_key(|path| {
                std::fs::metadata(path)
                    .and_then(|m| m.modified())
                    .unwrap_or(std::time::SystemTime::UNIX_EPOCH)
            })
            .unwrap();
        Ok(latest_log.to_string_lossy().to_string())
    }
}

/// 打开日志目录
#[tauri::command]
pub async fn logs_open_directory(app_handle: AppHandle) -> Result<(), String> {
    let log_dir = app_handle
        .path()
        .app_log_dir()
        .map_err(|e| format!("获取日志目录失败: {}", e))?;

    // 确保日志目录存在
    std::fs::create_dir_all(&log_dir).map_err(|e| format!("创建日志目录失败: {}", e))?;

    // 使用系统默认程序打开目录
    #[cfg(target_os = "windows")]
    {
        std::process::Command::new("explorer")
            .arg(&log_dir)
            .spawn()
            .map_err(|e| format!("打开目录失败: {}", e))?;
    }

    #[cfg(target_os = "macos")]
    {
        std::process::Command::new("open")
            .arg(&log_dir)
            .spawn()
            .map_err(|e| format!("打开目录失败: {}", e))?;
    }

    #[cfg(target_os = "linux")]
    {
        std::process::Command::new("xdg-open")
            .arg(&log_dir)
            .spawn()
            .map_err(|e| format!("打开目录失败: {}", e))?;
    }

    Ok(())
}

/// 获取日志目录路径
#[tauri::command]
pub async fn logs_get_directory_path(app_handle: AppHandle) -> Result<String, String> {
    let log_dir = app_handle
        .path()
        .app_log_dir()
        .map_err(|e| format!("获取日志目录失败: {}", e))?;

    Ok(log_dir.to_string_lossy().to_string())
}

/// 清理旧日志文件
#[tauri::command]
pub async fn logs_cleanup_old_files(app_handle: AppHandle, days: u64) -> Result<String, String> {
    let log_dir = app_handle
        .path()
        .app_log_dir()
        .map_err(|e| format!("获取日志目录失败: {}", e))?;

    let cutoff_time = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs()
        - (days * 24 * 60 * 60);

    let mut deleted_count = 0;
    let entries = std::fs::read_dir(&log_dir).map_err(|e| format!("读取日志目录失败: {}", e))?;

    for entry in entries {
        let entry = entry.map_err(|e| format!("读取目录项失败: {}", e))?;
        let path = entry.path();

        if path.is_file() {
            if let Ok(metadata) = std::fs::metadata(&path) {
                if let Ok(modified) = metadata.modified() {
                    if let Ok(duration) = modified.duration_since(std::time::UNIX_EPOCH) {
                        if duration.as_secs() < cutoff_time {
                            if std::fs::remove_file(&path).is_ok() {
                                deleted_count += 1;
                            }
                        }
                    }
                }
            }
        }
    }

    Ok(format!("已删除 {} 个旧日志文件", deleted_count))
}
